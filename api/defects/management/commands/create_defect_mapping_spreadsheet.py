import os
from pathlib import Path

from django.core.management.base import BaseCommand

from api.defects.models import (
    DefectScores,
    Feature,
    Standard,
    StandardSubcategory,
)

# TODO: Account for words in different order? Account for stf name in f name, and vice versa?

MANAGEMENT_FOLDER = Path(__file__).parent
DATA_FOLDER = MANAGEMENT_FOLDER.parent / "baseline_data"


def clear():
    os.system('cls||clear')


def get_ds_stf_desc(ds):
    return ds.defect_description.replace(f"{ds.defect_code} - ", "").split(" - ", 1)[0]


def get_dml_stf_desc(dml):
    return dml.name.split(" - ", 1)[0]


def get_stf_data(ds_feature_map):
    stf_data = []
    for df in ds_feature_map:
        ds_stf_desc = get_ds_stf_desc(df["ds"])
        if ds_stf_desc not in [stf["display_name"] for stf in stf_data]:
            stf_data.append(
                {
                    "display_name": ds_stf_desc,
                    "potential_features": [{"feature":df["feature"], "count": 1}],
                }
            )
        else:
            for stf in stf_data:
                if stf["display_name"] == ds_stf_desc:
                    if df["feature"] not in [pf["feature"] for pf in stf["potential_features"]]:
                        stf["potential_features"].append({"feature": df["feature"], "count": 1})
                    else:
                        for pf in stf["potential_features"]:
                            if pf["feature"] == df["feature"]:
                                pf["count"] += 1
                                break
                    break

    return sorted(stf_data, key=lambda x: x["display_name"])


def get_recommended_feature(ds, all_features):
    recommended_feature = None
    dml = ds.defect_key

    if dml:
        dml_stf_desc = get_dml_stf_desc(dml)
        feature_qs = all_features.filter(display_name__iexact=dml_stf_desc)
        if feature_qs.exists():
            recommended_feature = feature_qs.first()
    if not recommended_feature:
        ds_stf_desc = get_ds_stf_desc(ds)
        feature_qs = all_features.filter(display_name__iexact=ds_stf_desc)
        if feature_qs.exists():
            recommended_feature = feature_qs.first()

    return recommended_feature


def create_ds_feature_map(defect_scores, all_features):
    ds_dicts = []
    for ds in defect_scores:
        ds_dicts.append(
            {
                "ds": ds,
                "feature": get_recommended_feature(ds, all_features),
            }
        )
    return ds_dicts


def create_standard_features(defect_scores, all_features, standard):
    print("\nLooking for unique features in this subcategory...")
    ds_feature_map = create_ds_feature_map(defect_scores, all_features)
    stf_data = get_stf_data(ds_feature_map)

    standard_features = []
    for stf in stf_data:
        features_to_display = []
        potential_features = [pf for pf in stf["potential_features"] if pf["feature"]]
        total_count = sum(pf["count"] for pf in potential_features)

        feature = None
        if total_count > 0:
            clear()
            print(f'Standard Feature:\n\n  "{stf["display_name"]}"\n')
            for pf in potential_features:
                feature_name = pf["feature"].display_name
                likelihood = round((pf["count"] / total_count) * 100)
                features_to_display.append({"feature_name": feature_name, "likelihood": likelihood})
            features_to_display_sorted = sorted(features_to_display, key=lambda x: x["likelihood"], reverse=True)
            
            print("Potential Features:\n")
            for i, ftd in enumerate(features_to_display_sorted, 1):
                print(f"  {i}. {ftd['feature_name']} ({ftd['likelihood']}%)")
            
            feature_number = input("\n\nSelect a feature, or press enter to select from the full list: ")
            if feature_number:
                selected_ftd = features_to_display_sorted[int(feature_number) - 1]
                feature = [pf["feature"] for pf in potential_features if pf["feature"].display_name == selected_ftd["feature_name"]][0]

        if not feature:
            clear()
            print(f'Standard Feature:\n\n  "{stf["display_name"]}"\n')
            print("All Features:\n")
            for i, f in enumerate(all_features, 1):
                print(f"  {i}. {f.display_name}")

            feature_number = input("\n\nSelect a feature: ")
            feature = all_features[int(feature_number) - 1]

        standard_features.append(
            {
                "standard": standard.id,
                "feature": feature.id,
                "display_name": stf["display_name"],
            }
        )
    
    return standard_features


def get_relevant_defect_scores(sub_category):
    return DefectScores.objects.filter(sub_standard=sub_category).filter(defect_code__isnull=False).order_by("id")


def get_standard_sub_category(standard):
    clear()
    standard_sub_categories = StandardSubcategory.objects.filter(standard_key=standard).order_by("id")
    for sub_standard in standard_sub_categories:
        print(f"{sub_standard.id} - {sub_standard.comment}")
    return standard_sub_categories.get(id=int(input("\nSelect a subcategory by id: ")))


def get_standard():
    clear()
    all_standards = Standard.objects.all().order_by("id")
    for standard in all_standards:
        print(f"{standard.id} - {standard.display_name}")
    return all_standards.get(id=int(input("\nSelect a standard by id: ")))


class Command(BaseCommand):
    help = "Create a spreadsheet for mapping defects to the new schema for a single subcategory."

    def add_arguments(self, parser):
        pass

    def handle(self, *args, **options):
        """Handle the command execution."""
        standard = get_standard()
        sub_category = get_standard_sub_category(standard)
        defect_scores = get_relevant_defect_scores(sub_category)

        all_features = Feature.objects.all().order_by("display_name")
        standard_features = create_standard_features(defect_scores, all_features, standard)
        print(standard_features)
